#!/bin/bash

# Oracle Cloud Free Tier Optimization Script
# Tối ưu hóa server cho Oracle Cloud Free Tier (1GB RAM, 1 OCPU)

set -e

echo "🔧 Oracle Cloud Free Tier Optimization"
echo "======================================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Create swap file for additional memory
create_swap() {
    log_info "Tạo swap file để tăng memory..."
    
    if [ ! -f /swapfile ]; then
        sudo fallocate -l 1G /swapfile
        sudo chmod 600 /swapfile
        sudo mkswap /swapfile
        sudo swapon /swapfile
        
        # Make swap permanent
        echo '/swapfile none swap sw 0 0' | sudo tee -a /etc/fstab
        
        # Optimize swap usage
        echo 'vm.swappiness=10' | sudo tee -a /etc/sysctl.conf
        echo 'vm.vfs_cache_pressure=50' | sudo tee -a /etc/sysctl.conf
        
        log_info "✅ Swap file đã được tạo (1GB)"
    else
        log_info "Swap file đã tồn tại"
    fi
}

# Optimize system settings
optimize_system() {
    log_info "Tối ưu hóa cài đặt hệ thống..."
    
    # Reduce memory usage
    sudo sysctl -w vm.overcommit_memory=1
    sudo sysctl -w vm.max_map_count=262144
    
    # Optimize network settings
    sudo sysctl -w net.core.somaxconn=65535
    sudo sysctl -w net.ipv4.tcp_max_syn_backlog=65535
    
    # Make changes permanent
    cat << EOF | sudo tee -a /etc/sysctl.conf
# Oracle Cloud Optimizations
vm.overcommit_memory=1
vm.max_map_count=262144
net.core.somaxconn=65535
net.ipv4.tcp_max_syn_backlog=65535
EOF
}

# Install and configure log rotation
setup_log_rotation() {
    log_info "Cấu hình log rotation..."
    
    cat << EOF | sudo tee /etc/logrotate.d/commilive
/home/<USER>/commilive/logs/*.log {
    daily
    missingok
    rotate 7
    compress
    delaycompress
    notifempty
    create 644 ubuntu ubuntu
    postrotate
        docker-compose -f /home/<USER>/commilive/docker-compose.yml restart commilive
    endscript
}
EOF
}

# Setup automatic cleanup
setup_cleanup() {
    log_info "Thiết lập tự động dọn dẹp..."
    
    # Create cleanup script
    cat << 'EOF' > cleanup.sh
#!/bin/bash
# Automatic cleanup script for Oracle Cloud Free Tier

# Clean Docker
docker system prune -f
docker volume prune -f

# Clean logs older than 7 days
find logs/ -name "*.log" -mtime +7 -delete

# Clean temporary files
rm -rf /tmp/*
rm -rf /var/tmp/*

# Clean package cache
sudo apt-get clean
sudo apt-get autoclean

echo "Cleanup completed at $(date)"
EOF

    chmod +x cleanup.sh
    
    # Add to crontab (run daily at 2 AM)
    (crontab -l 2>/dev/null; echo "0 2 * * * /home/<USER>/commilive/cleanup.sh >> /home/<USER>/commilive/logs/cleanup.log 2>&1") | crontab -
}

# Configure Docker for low memory
optimize_docker() {
    log_info "Tối ưu hóa Docker cho low memory..."
    
    # Create Docker daemon configuration
    sudo mkdir -p /etc/docker
    cat << EOF | sudo tee /etc/docker/daemon.json
{
    "log-driver": "json-file",
    "log-opts": {
        "max-size": "10m",
        "max-file": "3"
    },
    "storage-driver": "overlay2",
    "default-ulimits": {
        "nofile": {
            "Name": "nofile",
            "Hard": 64000,
            "Soft": 64000
        }
    }
}
EOF

    sudo systemctl restart docker
}

# Setup monitoring
setup_monitoring() {
    log_info "Thiết lập monitoring cơ bản..."
    
    # Create monitoring script
    cat << 'EOF' > monitor.sh
#!/bin/bash
# Basic monitoring script

LOG_FILE="logs/monitor.log"
DATE=$(date '+%Y-%m-%d %H:%M:%S')

# Check memory usage
MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')

# Check disk usage
DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')

# Check if services are running
DOCKER_STATUS=$(docker-compose ps | grep -c "Up" || echo "0")

# Log status
echo "[$DATE] Memory: ${MEMORY_USAGE}%, Disk: ${DISK_USAGE}%, Services: ${DOCKER_STATUS}" >> $LOG_FILE

# Alert if memory usage > 90%
if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
    echo "[$DATE] WARNING: High memory usage: ${MEMORY_USAGE}%" >> $LOG_FILE
    # Restart services if memory is too high
    docker-compose restart
fi

# Alert if disk usage > 85%
if [ "$DISK_USAGE" -gt 85 ]; then
    echo "[$DATE] WARNING: High disk usage: ${DISK_USAGE}%" >> $LOG_FILE
    # Run cleanup
    ./cleanup.sh
fi
EOF

    chmod +x monitor.sh
    
    # Run monitoring every 5 minutes
    (crontab -l 2>/dev/null; echo "*/5 * * * * /home/<USER>/commilive/monitor.sh") | crontab -
}

# Setup backup
setup_backup() {
    log_info "Thiết lập backup tự động..."
    
    mkdir -p backup
    
    # Create backup script
    cat << 'EOF' > backup.sh
#!/bin/bash
# Backup script for CommiLive

BACKUP_DIR="backup"
DATE=$(date +%Y%m%d_%H%M%S)

# Backup database
if [ -f "data/instagram_live.db" ]; then
    cp "data/instagram_live.db" "$BACKUP_DIR/instagram_live_$DATE.db"
    echo "Database backed up: instagram_live_$DATE.db"
fi

# Backup configuration
tar -czf "$BACKUP_DIR/config_$DATE.tar.gz" .env docker-compose.yml nginx.conf

# Keep only last 7 backups
find $BACKUP_DIR -name "*.db" -mtime +7 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +7 -delete

echo "Backup completed at $(date)"
EOF

    chmod +x backup.sh
    
    # Run backup daily at 1 AM
    (crontab -l 2>/dev/null; echo "0 1 * * * /home/<USER>/commilive/backup.sh >> /home/<USER>/commilive/logs/backup.log 2>&1") | crontab -
}

# Main optimization process
main() {
    log_info "Bắt đầu tối ưu hóa Oracle Cloud Free Tier..."
    
    create_swap
    optimize_system
    setup_log_rotation
    setup_cleanup
    optimize_docker
    setup_monitoring
    setup_backup
    
    log_info "🎉 Tối ưu hóa hoàn tất!"
    echo
    echo "📊 Thông tin hệ thống:"
    echo "   Memory: $(free -h | grep Mem | awk '{print $3"/"$2}')"
    echo "   Disk:   $(df -h / | awk 'NR==2 {print $3"/"$2" ("$5" used)"}')"
    echo "   Swap:   $(free -h | grep Swap | awk '{print $2}')"
    echo
    echo "🔧 Scripts đã được tạo:"
    echo "   ./cleanup.sh  - Dọn dẹp hệ thống"
    echo "   ./monitor.sh  - Giám sát hệ thống"
    echo "   ./backup.sh   - Backup dữ liệu"
    echo
    echo "⏰ Cron jobs đã được thiết lập:"
    echo "   Daily cleanup at 2:00 AM"
    echo "   Daily backup at 1:00 AM"
    echo "   System monitoring every 5 minutes"
}

# Run main function
main "$@"
