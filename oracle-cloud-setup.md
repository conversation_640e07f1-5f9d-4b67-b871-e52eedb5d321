# Hướng dẫn Deploy CommiLive lên Oracle Cloud Free Tier

## 1. <PERSON><PERSON><PERSON> <PERSON>à<PERSON><PERSON>n Oracle Cloud Free Tier

1. <PERSON><PERSON><PERSON> c<PERSON><PERSON> [Oracle Cloud Free Tier](https://www.oracle.com/cloud/free/)
2. Đ<PERSON>ng ký tài khoản miễn phí (cần thẻ tín dụng để xác thực)
3. <PERSON><PERSON><PERSON> nhập vào Oracle Cloud Console

## 2. Tạo VM Instance

### Bước 1: Tạo Compute Instance
1. Vào **Compute** > **Instances**
2. Click **Create Instance**
3. Cấu hình:
   - **Name**: `commilive-server`
   - **Image**: Ubuntu 22.04 LTS
   - **Shape**: VM.Standard.E2.1.Micro (Always Free)
   - **Boot Volume**: 50GB (tối đa cho Free Tier)

### Bước 2: Cấu hình Network
1. Tạo **Virtual Cloud Network (VCN)**:
   - Name: `commilive-vcn`
   - CIDR: `10.0.0.0/16`

2. Tạo **Subnet**:
   - Name: `commilive-subnet`
   - CIDR: `********/24`
   - Type: Public

3. Cấu hình **Security List**:
   - Ingress Rules:
     - Port 22 (SSH): 0.0.0.0/0
     - Port 80 (HTTP): 0.0.0.0/0
     - Port 443 (HTTPS): 0.0.0.0/0
     - Port 3001 (App): 0.0.0.0/0
     - Port 5000 (Python): 0.0.0.0/0

### Bước 3: SSH Key
1. Tạo SSH key pair hoặc upload public key
2. Lưu private key để kết nối SSH

## 3. Kết nối và Setup Server

### Kết nối SSH
```bash
ssh -i your-private-key.pem ubuntu@your-instance-ip
```

### Upload code lên server
```bash
# Trên máy local
scp -i your-private-key.pem -r . ubuntu@your-instance-ip:~/commilive/

# Hoặc clone từ Git
ssh -i your-private-key.pem ubuntu@your-instance-ip
git clone https://github.com/your-repo/commilive.git
cd commilive
```

## 4. Chạy script deploy

```bash
chmod +x deploy-oracle.sh
./deploy-oracle.sh
```

## 5. Cấu hình Domain (Tùy chọn)

### Sử dụng domain riêng:
1. Mua domain từ nhà cung cấp
2. Tạo A record trỏ về IP của Oracle Cloud instance
3. Cập nhật biến `DOMAIN` trong script deploy

### Sử dụng IP public:
- Truy cập trực tiếp qua IP: `https://your-instance-ip`

## 6. Monitoring và Maintenance

### Kiểm tra logs:
```bash
docker-compose logs -f
```

### Restart services:
```bash
docker-compose restart
```

### Update ứng dụng:
```bash
git pull
docker-compose build
docker-compose up -d
```

### Backup database:
```bash
cp data/instagram_live.db backup/instagram_live_$(date +%Y%m%d_%H%M%S).db
```

## 7. Tối ưu hóa cho Oracle Cloud Free Tier

### Resource Limits:
- RAM: 1GB
- CPU: 1 OCPU (2 vCPUs)
- Storage: 50GB Boot Volume
- Bandwidth: 10TB/month

### Tối ưu hóa:
1. **Memory**: Giới hạn memory cho Docker containers
2. **CPU**: Sử dụng PM2 để quản lý Node.js processes
3. **Storage**: Định kỳ clean up logs và temporary files
4. **Network**: Sử dụng CDN cho static assets

## 8. Security Best Practices

1. **Firewall**: Chỉ mở ports cần thiết
2. **SSH**: Disable password authentication, chỉ dùng key
3. **SSL**: Sử dụng Let's Encrypt cho HTTPS
4. **Updates**: Định kỳ update system và dependencies
5. **Backup**: Tự động backup database và configs

## 9. Troubleshooting

### Lỗi thường gặp:

1. **Out of Memory**:
   ```bash
   # Tạo swap file
   sudo fallocate -l 1G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

2. **Port không accessible**:
   - Kiểm tra Security List trong Oracle Cloud Console
   - Kiểm tra iptables rules trên server

3. **Docker build fails**:
   - Kiểm tra disk space: `df -h`
   - Clean up Docker: `docker system prune -a`

4. **SSL certificate issues**:
   - Kiểm tra domain DNS settings
   - Renew certificate: `sudo certbot renew`

## 10. Cost Monitoring

Oracle Cloud Free Tier bao gồm:
- 2 VM instances (Always Free)
- 200GB Block Storage
- 10GB Object Storage
- 10TB Outbound Data Transfer/month

Theo dõi usage tại Oracle Cloud Console để tránh vượt quá giới hạn miễn phí.
