#!/bin/sh

# Start Python Flask service in background
echo "Starting Python InstagrapiService..."
cd /app/src/backend/services
python3 InstagrapiService.py &
PYTHON_PID=$!

# Wait a moment for Python service to start
sleep 5

# Start Node.js server
echo "Starting Node.js server..."
cd /app
node src/backend/server.js &
NODE_PID=$!

# Function to handle shutdown
shutdown() {
    echo "Shutting down services..."
    kill $PYTHON_PID 2>/dev/null
    kill $NODE_PID 2>/dev/null
    exit 0
}

# Trap signals
trap shutdown SIGTERM SIGINT

# Wait for processes
wait $NODE_PID
