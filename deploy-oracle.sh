#!/bin/bash

# CommiLive Oracle Cloud Deployment Script
# Sử dụng script này để deploy ứng dụng lên Oracle Cloud Free Tier

set -e

echo "🚀 CommiLive Oracle Cloud Deployment Script"
echo "==========================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
DOMAIN=${DOMAIN:-"your-domain.com"}
EMAIL=${EMAIL:-"<EMAIL>"}
APP_NAME="commilive"

# Functions
log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
check_root() {
    if [[ $EUID -eq 0 ]]; then
        log_error "Không nên chạy script này với quyền root!"
        exit 1
    fi
}

# Update system
update_system() {
    log_info "Cập nhật hệ thống..."
    sudo apt update && sudo apt upgrade -y
    sudo apt install -y curl wget git unzip
}

# Install Docker
install_docker() {
    log_info "Cài đặt Docker..."
    
    if ! command -v docker &> /dev/null; then
        curl -fsSL https://get.docker.com -o get-docker.sh
        sudo sh get-docker.sh
        sudo usermod -aG docker $USER
        rm get-docker.sh
        log_info "Docker đã được cài đặt. Vui lòng logout và login lại để áp dụng quyền."
    else
        log_info "Docker đã được cài đặt."
    fi
}

# Install Docker Compose
install_docker_compose() {
    log_info "Cài đặt Docker Compose..."
    
    if ! command -v docker-compose &> /dev/null; then
        sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
        sudo chmod +x /usr/local/bin/docker-compose
    else
        log_info "Docker Compose đã được cài đặt."
    fi
}

# Setup firewall
setup_firewall() {
    log_info "Cấu hình firewall..."
    
    # Oracle Cloud sử dụng iptables, không phải ufw
    sudo iptables -I INPUT 1 -p tcp --dport 80 -j ACCEPT
    sudo iptables -I INPUT 1 -p tcp --dport 443 -j ACCEPT
    sudo iptables -I INPUT 1 -p tcp --dport 3001 -j ACCEPT
    sudo iptables -I INPUT 1 -p tcp --dport 5000 -j ACCEPT
    
    # Save iptables rules
    sudo iptables-save | sudo tee /etc/iptables/rules.v4
}

# Create directories
create_directories() {
    log_info "Tạo thư mục cần thiết..."
    mkdir -p data logs ssl
    chmod 755 data logs ssl
}

# Generate SSL certificate (self-signed for testing)
generate_ssl() {
    log_info "Tạo SSL certificate tự ký..."
    
    if [ ! -f ssl/cert.pem ]; then
        openssl req -x509 -nodes -days 365 -newkey rsa:2048 \
            -keyout ssl/key.pem \
            -out ssl/cert.pem \
            -subj "/C=VN/ST=HCM/L=HCM/O=CommiLive/CN=${DOMAIN}"
        
        chmod 600 ssl/key.pem
        chmod 644 ssl/cert.pem
    else
        log_info "SSL certificate đã tồn tại."
    fi
}

# Setup Let's Encrypt (optional)
setup_letsencrypt() {
    read -p "Bạn có muốn cài đặt Let's Encrypt SSL certificate? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        log_info "Cài đặt Certbot..."
        sudo apt install -y certbot
        
        log_warn "Hãy đảm bảo domain ${DOMAIN} đã trỏ về IP của server này."
        read -p "Nhấn Enter để tiếp tục..."
        
        sudo certbot certonly --standalone -d ${DOMAIN} --email ${EMAIL} --agree-tos --non-interactive
        
        # Copy certificates
        sudo cp /etc/letsencrypt/live/${DOMAIN}/fullchain.pem ssl/cert.pem
        sudo cp /etc/letsencrypt/live/${DOMAIN}/privkey.pem ssl/key.pem
        sudo chown $USER:$USER ssl/cert.pem ssl/key.pem
    fi
}

# Build and start application
start_application() {
    log_info "Build và khởi động ứng dụng..."
    
    # Build Docker image
    docker-compose build
    
    # Start services
    docker-compose up -d
    
    log_info "Ứng dụng đang khởi động..."
    sleep 10
    
    # Check if services are running
    if docker-compose ps | grep -q "Up"; then
        log_info "✅ Ứng dụng đã khởi động thành công!"
        echo
        echo "🌐 Truy cập ứng dụng tại:"
        echo "   HTTP:  http://$(curl -s ifconfig.me)"
        echo "   HTTPS: https://$(curl -s ifconfig.me)"
        echo
        echo "📊 Kiểm tra logs:"
        echo "   docker-compose logs -f"
        echo
        echo "🔧 Quản lý services:"
        echo "   docker-compose stop    # Dừng services"
        echo "   docker-compose start   # Khởi động services"
        echo "   docker-compose restart # Khởi động lại services"
    else
        log_error "❌ Có lỗi khi khởi động ứng dụng!"
        docker-compose logs
        exit 1
    fi
}

# Main deployment process
main() {
    log_info "Bắt đầu quá trình deploy CommiLive lên Oracle Cloud..."
    
    check_root
    update_system
    install_docker
    install_docker_compose
    setup_firewall
    create_directories
    generate_ssl
    setup_letsencrypt
    start_application
    
    log_info "🎉 Deploy hoàn tất!"
}

# Run main function
main "$@"
