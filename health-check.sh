#!/bin/bash

# Health Check Script for CommiLive on Oracle Cloud
# Kiểm tra tình trạng hoạt động của các services

set -e

echo "🏥 CommiLive Health Check"
echo "========================"

# Colors
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m'

log_info() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check system resources
check_system() {
    echo "📊 System Resources:"
    echo "==================="
    
    # Memory usage
    MEMORY_USAGE=$(free | grep Mem | awk '{printf "%.1f", $3/$2 * 100.0}')
    MEMORY_TOTAL=$(free -h | grep Mem | awk '{print $2}')
    MEMORY_USED=$(free -h | grep Mem | awk '{print $3}')
    
    echo "Memory: $MEMORY_USED / $MEMORY_TOTAL (${MEMORY_USAGE}%)"
    
    if (( $(echo "$MEMORY_USAGE > 90" | bc -l) )); then
        log_error "High memory usage!"
    elif (( $(echo "$MEMORY_USAGE > 75" | bc -l) )); then
        log_warn "Memory usage is getting high"
    else
        log_info "Memory usage is normal"
    fi
    
    # Disk usage
    DISK_USAGE=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    DISK_TOTAL=$(df -h / | awk 'NR==2 {print $2}')
    DISK_USED=$(df -h / | awk 'NR==2 {print $3}')
    
    echo "Disk: $DISK_USED / $DISK_TOTAL (${DISK_USAGE}%)"
    
    if [ "$DISK_USAGE" -gt 90 ]; then
        log_error "High disk usage!"
    elif [ "$DISK_USAGE" -gt 75 ]; then
        log_warn "Disk usage is getting high"
    else
        log_info "Disk usage is normal"
    fi
    
    # CPU load
    CPU_LOAD=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    echo "CPU Load: $CPU_LOAD"
    
    echo
}

# Check Docker services
check_docker() {
    echo "🐳 Docker Services:"
    echo "=================="
    
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed!"
        return 1
    fi
    
    if ! docker info &> /dev/null; then
        log_error "Docker daemon is not running!"
        return 1
    fi
    
    log_info "Docker daemon is running"
    
    # Check Docker Compose services
    if [ -f "docker-compose.yml" ]; then
        echo
        echo "Services status:"
        docker-compose ps
        
        # Check if services are running
        RUNNING_SERVICES=$(docker-compose ps | grep "Up" | wc -l)
        TOTAL_SERVICES=$(docker-compose ps | grep -v "Name\|----" | wc -l)
        
        echo
        echo "Running services: $RUNNING_SERVICES / $TOTAL_SERVICES"
        
        if [ "$RUNNING_SERVICES" -eq "$TOTAL_SERVICES" ]; then
            log_info "All services are running"
        else
            log_error "Some services are not running!"
        fi
    else
        log_warn "docker-compose.yml not found"
    fi
    
    echo
}

# Check application endpoints
check_endpoints() {
    echo "🌐 Application Endpoints:"
    echo "========================"
    
    # Check main application
    if curl -f -s http://localhost:3001/api/health > /dev/null; then
        log_info "Main application (port 3001) is responding"
    else
        log_error "Main application (port 3001) is not responding!"
    fi
    
    # Check Python service
    if curl -f -s http://localhost:5000/ > /dev/null; then
        log_info "Python service (port 5000) is responding"
    else
        log_error "Python service (port 5000) is not responding!"
    fi
    
    # Check Nginx
    if curl -f -s http://localhost:80 > /dev/null; then
        log_info "Nginx (port 80) is responding"
    else
        log_warn "Nginx (port 80) is not responding"
    fi
    
    echo
}

# Check logs for errors
check_logs() {
    echo "📋 Recent Logs:"
    echo "=============="
    
    if [ -d "logs" ]; then
        echo "Recent errors in logs:"
        find logs/ -name "*.log" -mtime -1 -exec grep -i "error\|exception\|failed" {} \; | tail -10
        echo
        
        echo "Log file sizes:"
        du -sh logs/* 2>/dev/null || echo "No log files found"
    else
        log_warn "Logs directory not found"
    fi
    
    echo
}

# Check database
check_database() {
    echo "🗄️ Database:"
    echo "============"
    
    if [ -f "data/instagram_live.db" ]; then
        DB_SIZE=$(du -sh data/instagram_live.db | cut -f1)
        log_info "Database file exists (size: $DB_SIZE)"
        
        # Check if database is accessible
        if sqlite3 data/instagram_live.db "SELECT COUNT(*) FROM sqlite_master;" > /dev/null 2>&1; then
            log_info "Database is accessible"
        else
            log_error "Database is not accessible!"
        fi
    else
        log_warn "Database file not found"
    fi
    
    echo
}

# Check network connectivity
check_network() {
    echo "🌍 Network Connectivity:"
    echo "======================="
    
    # Check internet connectivity
    if ping -c 1 google.com > /dev/null 2>&1; then
        log_info "Internet connectivity is working"
    else
        log_error "No internet connectivity!"
    fi
    
    # Check if ports are open
    if netstat -tuln | grep -q ":3001 "; then
        log_info "Port 3001 is listening"
    else
        log_warn "Port 3001 is not listening"
    fi
    
    if netstat -tuln | grep -q ":5000 "; then
        log_info "Port 5000 is listening"
    else
        log_warn "Port 5000 is not listening"
    fi
    
    echo
}

# Generate summary report
generate_summary() {
    echo "📋 Health Check Summary:"
    echo "======================="
    
    TIMESTAMP=$(date '+%Y-%m-%d %H:%M:%S')
    echo "Check performed at: $TIMESTAMP"
    echo
    
    # Save summary to file
    {
        echo "CommiLive Health Check Report"
        echo "Generated at: $TIMESTAMP"
        echo "=============================="
        echo
        echo "System Resources:"
        echo "- Memory: $(free -h | grep Mem | awk '{print $3"/"$2}')"
        echo "- Disk: $(df -h / | awk 'NR==2 {print $3"/"$2}')"
        echo "- CPU Load: $(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')"
        echo
        echo "Services Status:"
        docker-compose ps 2>/dev/null || echo "Docker Compose not available"
        echo
    } > logs/health-check-$(date +%Y%m%d_%H%M%S).log
    
    log_info "Health check report saved to logs/"
}

# Main health check process
main() {
    log_info "Starting CommiLive health check..."
    echo
    
    check_system
    check_docker
    check_endpoints
    check_logs
    check_database
    check_network
    generate_summary
    
    log_info "🎉 Health check completed!"
}

# Run main function
main "$@"
