# Server Configuration
PORT=3001
NODE_ENV=production
HOST=0.0.0.0

# Database Configuration
DB_PATH=./src/backend/data/instagram_live.db

# Redis Configuration (for message queue)
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=

# Instagram Configuration
INSTAGRAM_USERNAME=
INSTAGRAM_PASSWORD=
INSTAGRAM_LIVE_URL=

# Security
JWT_SECRET=your-super-secret-jwt-key-here
ENCRYPTION_KEY=your-32-character-encryption-key

# Puppeteer Configuration
PUPPETEER_HEADLESS=false
PUPPETEER_TIMEOUT=30000
PUPPETEER_VIEWPORT_WIDTH=1366
PUPPETEER_VIEWPORT_HEIGHT=768

# Message Queue Configuration
QUEUE_CONCURRENCY=1
QUEUE_RETRY_ATTEMPTS=3
QUEUE_RETRY_DELAY=5000

# Rate Limiting
RATE_LIMIT_WINDOW_MS=900000
RATE_LIMIT_MAX_REQUESTS=100

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=./logs

# Features
ENABLE_AUTO_MESSAGING=true
ENABLE_COMMENT_FILTERING=true
ENABLE_ORDER_DETECTION=true
ENABLE_PRICE_INQUIRY_DETECTION=true

# Notification Settings
ENABLE_DESKTOP_NOTIFICATIONS=true
ENABLE_SOUND_NOTIFICATIONS=true

# Performance - Unlimited comments support
MAX_COMMENTS_IN_MEMORY=100000
COMMENT_CLEANUP_INTERVAL=0

# Development
DEBUG_MODE=false
MOCK_INSTAGRAM_DATA=false

# Python Service Configuration
PYTHON_SERVICE_PORT=5000
PYTHON_SERVICE_URL=http://localhost:5000

# SSL Configuration (for production)
SSL_CERT_PATH=./ssl/cert.pem
SSL_KEY_PATH=./ssl/key.pem

# Oracle Cloud Specific
ORACLE_CLOUD_REGION=ap-singapore-1
DOMAIN=your-domain.com
EMAIL=<EMAIL>

# Session Configuration
SESSION_SECRET=your-super-secret-session-key-here
